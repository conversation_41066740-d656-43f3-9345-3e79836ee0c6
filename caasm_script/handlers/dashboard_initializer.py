from caasm_config.config import caasm_config
from caasm_script.handlers._base import _InitializeHandler
from caasm_service.runtime import (
    overview_chart_category_service,
    user_service,
    overview_chart_service,
    overview_service,
    file_manage_service,
    meta_view_service,
)


class DashboardInitializer(_InitializeHandler):
    def __init__(self, *args, **kwargs):
        super(DashboardInitializer, self).__init__(*args, **kwargs)
        self.no_delete_default_names = []

    @classmethod
    def name(cls):
        return "dashboard"

    def init_step_1_empty(self):
        space_data = overview_service.find_owner_space(is_default=True)
        delete_space_ids = []
        for space in space_data:
            delete = False
            charts_info = space.charts
            # for chart in charts_info:
            #     chart_data = overview_chart_service.get_chart_info(chart_id=chart.chart)
            #     if not chart_data.internal:
            #         delete = True
            #         break
            delete_space_ids.append(space.id)
        overview_service.delete_space(space_ids=delete_space_ids) if delete_space_ids else ...

    def init_step_2_space(self):
        create_asset_fusion()
        create_security_metrics()


def create_space(name=None, chart_info=None, index=0):
    user_id = str(user_service.get_user(username=caasm_config.DEFAULT_SUPER_USER).id)
    space_one = {
        "icon": None,
        "index": index,
        "name": name,
        "ownership": "public",
        "charts": chart_info,
        "category": "dashboard",
        "user_id": user_id,
        "is_default": True,
        "role_codes": [],
    }
    return overview_service.add_new_space(overview_service.load_entity(**space_one))


def creat_chart(
    chart_name=None,
    description=None,
    internal=True,
    chart_type=None,
    metric_type=None,
    category=None,
    f_category=None,
    s_category=None,
    base_query=None,
    count_info=None,
    group_fields=None,
    asql=None,
    time=None,
    table=None,
    entity_type=None,
):
    category_tree_id = str(overview_chart_category_service.get_chart_category(category_name="未分组").id)
    chart_name = chart_name
    description = ""
    creator_id = str(user_service.get_user(username=caasm_config.DEFAULT_SUPER_USER).id)
    internal = internal
    chart_type = chart_type
    metric_type = metric_type
    f_category = f_category
    s_category = s_category
    category = category
    base_query = base_query
    count_info = count_info
    group_fields = group_fields
    asql = asql
    time = time
    table = table
    data = {
        "base_query": base_query or "",
        "chart_name": chart_name,
        "category_tree_id": category_tree_id,
        "description": description,
        "creator_id": creator_id,
        "chart_type": chart_type,
        "metric_type": metric_type,
        "internal": True,
        "category": category,
        "entity_type": entity_type,
    }
    if s_category:
        data["s_category"] = s_category
    if f_category:
        data["f_category"] = f_category
    if base_query:
        data["base_query"] = base_query
    if count_info:
        data["count_info"] = count_info
    if group_fields:
        data["group_fields"] = group_fields
    if asql:
        data["asql"] = asql
    if time:
        data["time"] = time
    if table:
        data["table"] = table

    chart = overview_chart_service.get_chart_info(chart_name=chart_name)
    if chart:
        overview_chart_service.delete_chart_info(chart_ids=[chart.id])
    return overview_chart_service.add_chart_info(overview_chart_service.load_entity(**data))


def create_security_metrics():
    hids_id = creat_chart(
        chart_name="主机HIDS覆盖",
        chart_type="ring",
        metric_type="asql",
        category="asset",
        asql={
            "querys": [
                {
                    "value": "",
                    "label": "有防护",
                    "aql": '$.asset_base.adapter_properties="HIDS" and $.base.asset_type_display_name="主机"',
                    "type": "asql",
                },
                {
                    "value": "",
                    "label": "无防护",
                    "aql": 'not $.asset_base.adapter_properties="HIDS" and $.base.asset_type_display_name="主机"',
                    "type": "asql",
                },
            ]
        },
    )

    hids_id = creat_chart(
        chart_name="主机HIDS覆盖",
        chart_type="ring",
        metric_type="asql",
        category="asset",
        asql={
            "querys": [
                {
                    "value": "",
                    "label": "有防护",
                    "aql": '$.asset_base.adapter_properties="HIDS" and $.base.asset_type_display_name="主机"',
                    "type": "asql",
                },
                {
                    "value": "",
                    "label": "无防护",
                    "aql": 'not $.asset_base.adapter_properties="HIDS" and $.base.asset_type_display_name="主机"',
                    "type": "asql",
                },
            ]
        },
    )

    loophole_id = creat_chart(
        chart_name="主机漏扫覆盖",
        chart_type="ring",
        metric_type="asql",
        category="asset",
        asql={
            "querys": [
                {
                    "value": "",
                    "label": "已覆盖",
                    "aql": '$.asset_base.adapter_properties="漏洞扫描" and $.base.asset_type_display_name="主机"',
                    "type": "asql",
                },
                {
                    "value": "",
                    "label": "未覆盖",
                    "aql": 'not $.asset_base.adapter_properties="漏洞扫描" and $.base.asset_type_display_name="主机"',
                    "type": "asql",
                },
            ]
        },
    )

    cmdb_id = creat_chart(
        chart_name="主机CMDB覆盖",
        chart_type="ring",
        metric_type="asql",
        category="asset",
        asql={
            "querys": [
                {
                    "value": "",
                    "label": "已安装",
                    "aql": '$.asset_base.adapter_properties="CMDB" and $.base.asset_type_display_name="主机"',
                    "type": "asql",
                },
                {
                    "value": "",
                    "label": "未安装",
                    "aql": 'not $.asset_base.adapter_properties="CMDB" and $.base.asset_type_display_name="主机"',
                    "type": "asql",
                },
            ]
        },
    )

    fortress_machine_id = creat_chart(
        chart_name="主机堡垒机覆盖",
        chart_type="ring",
        metric_type="asql",
        category="asset",
        asql={
            "querys": [
                {
                    "value": "",
                    "label": "有防护",
                    "aql": '$.asset_base.adapter_properties="堡垒机" and $.base.asset_type_display_name="主机"',
                    "type": "asql",
                },
                {
                    "value": "",
                    "label": "无防护",
                    "aql": 'not $.asset_base.adapter_properties="堡垒机" and $.base.asset_type_display_name="主机"',
                    "type": "asql",
                },
            ]
        },
    )

    z_edr_id = creat_chart(
        chart_name="终端EDR覆盖",
        chart_type="ring",
        metric_type="asql",
        category="asset",
        asql={
            "querys": [
                {
                    "value": "",
                    "label": "已覆盖",
                    "aql": '$.asset_base.adapter_properties="终端防护" and $.base.asset_type_display_name="终端"',
                    "type": "asql",
                },
                {
                    "value": "",
                    "label": "未覆盖",
                    "aql": 'not $.asset_base.adapter_properties="终端防护" and $.base.asset_type_display_name="终端"',
                    "type": "asql",
                },
            ]
        },
    )

    z_loophole_id = creat_chart(
        chart_name="终端漏扫覆盖",
        chart_type="ring",
        metric_type="asql",
        category="asset",
        asql={
            "querys": [
                {
                    "value": "",
                    "label": "已覆盖",
                    "aql": '$.asset_base.adapter_properties="漏洞扫描" and $.base.asset_type_display_name="终端"',
                    "type": "asql",
                },
                {
                    "value": "",
                    "label": "未覆盖",
                    "aql": 'not $.asset_base.adapter_properties="漏洞扫描" and $.base.asset_type_display_name="终端"',
                    "type": "asql",
                },
            ]
        },
    )

    z_av_id = creat_chart(
        chart_name="终端AV覆盖",
        chart_type="ring",
        metric_type="asql",
        category="asset",
        asql={
            "querys": [
                {
                    "value": "",
                    "label": "已覆盖",
                    "aql": '$.asset_base.adapter_properties="防病毒" and $.base.asset_type_display_name="终端"',
                    "type": "asql",
                },
                {
                    "value": "",
                    "label": "未覆盖",
                    "aql": 'not $.asset_base.adapter_properties="防病毒" and $.base.asset_type_display_name="终端"',
                    "type": "asql",
                },
            ]
        },
    )

    z_zr_id = creat_chart(
        chart_name="终端准入覆盖",
        chart_type="ring",
        metric_type="asql",
        category="asset",
        asql={
            "querys": [
                {
                    "value": "",
                    "label": "已覆盖",
                    "aql": '$.asset_base.adapter_properties="终端准入" and $.base.asset_type_display_name="终端"',
                    "type": "asql",
                },
                {
                    "value": "",
                    "label": "未覆盖",
                    "aql": 'not $.asset_base.adapter_properties="终端准入" and $.base.asset_type_display_name="终端"',
                    "type": "asql",
                },
            ]
        },
    )
    security_id = create_space(
        name="安全度量",
        index=2,
        chart_info=[
            {
                "chart": str(hids_id),
                "chart_instance_name": "主机HIDS覆盖",
                "horizontal_index": 0,
                "vertical_index": 0,
                "width": 2,
                "height": 4,
            },
            {
                "chart": str(loophole_id),
                "chart_instance_name": "主机漏扫覆盖",
                "horizontal_index": 2,
                "vertical_index": 0,
                "width": 2,
                "height": 4,
            },
            {
                "chart": str(cmdb_id),
                "chart_instance_name": "主机CMDB覆盖",
                "horizontal_index": 4,
                "vertical_index": 0,
                "width": 2,
                "height": 4,
            },
            {
                "chart": str(fortress_machine_id),
                "chart_instance_name": "主机堡垒机覆盖",
                "horizontal_index": 6,
                "vertical_index": 0,
                "width": 2,
                "height": 4,
            },
            {
                "chart": str(z_edr_id),
                "chart_instance_name": "终端EDR覆盖",
                "horizontal_index": 0,
                "vertical_index": 4,
                "width": 2,
                "height": 4,
            },
            {
                "chart": str(z_loophole_id),
                "chart_instance_name": "终端漏扫覆盖",
                "horizontal_index": 2,
                "vertical_index": 4,
                "width": 2,
                "height": 4,
            },
            {
                "chart": str(z_av_id),
                "chart_instance_name": "终端AV覆盖",
                "horizontal_index": 4,
                "vertical_index": 4,
                "width": 2,
                "height": 4,
            },
            {
                "chart": str(z_zr_id),
                "chart_instance_name": "终端准入覆盖",
                "horizontal_index": 6,
                "vertical_index": 4,
                "width": 2,
                "height": 4,
            },
        ],
    )


def create_asset_fusion():
    adapter_chart_id = creat_chart(
        chart_name="资产发现",
        chart_type="adapter",
        metric_type="group",
        f_category="group",
        category="asset",
        group_fields=[{"field": "base.adapters", "limit": 10}],
        count_info={"field": "base.entity_id", "count_method": "value_count", "order": "desc", "percentage": "yes"},
    )
    host_asset_id = creat_chart(
        chart_name="主机资产",
        base_query='$.base.asset_type_display_name="主机"',
        metric_type="count",
        chart_type="text",
        category="asset",
        count_info={
            "field": "base.entity_id",
            "count_method": "value_count",
            "file": str(file_manage_service.get_file_manage_record(file_full_name="主机.png").file_id),
        },
        entity_type="host",
    )
    edr_asset_id = creat_chart(
        chart_name="终端资产",
        base_query='$.base.asset_type_display_name="终端"',
        metric_type="count",
        chart_type="text",
        category="asset",
        count_info={
            "field": "base.entity_id",
            "count_method": "value_count",
            "file": str(file_manage_service.get_file_manage_record(file_full_name="终端.png").file_id),
        },
        entity_type="terminal",
    )

    port_service_id = creat_chart(
        chart_name="端口服务",
        base_query='$.base.asset_type_display_name="端口服务"',
        metric_type="count",
        chart_type="text",
        category="asset",
        count_info={
            "field": "base.entity_id",
            "count_method": "value_count",
            "file": str(file_manage_service.get_file_manage_record(file_full_name="公网IP.png").file_id),
        },
        entity_type="port_server",
    )

    domain_id = creat_chart(
        chart_name="域名",
        base_query='$.base.asset_type_display_name="域名"',
        metric_type="count",
        chart_type="text",
        category="asset",
        count_info={
            "field": "base.entity_id",
            "count_method": "value_count",
            "file": str(file_manage_service.get_file_manage_record(file_full_name="域名.png").file_id),
        },
        entity_type="domain_name",
    )

    docker_id = creat_chart(
        chart_name="容器",
        base_query='$.base.asset_type_display_name="容器"',
        metric_type="count",
        chart_type="text",
        category="asset",
        count_info={
            "field": "base.entity_id",
            "count_method": "value_count",
            "file": str(file_manage_service.get_file_manage_record(file_full_name="容器.png").file_id),
        },
        entity_type="",
    )

    business_chart_id = creat_chart(
        chart_name="业务资产数量Top10",
        chart_type="bar",
        category="asset",
        entity_type="host",
        count_info={"field": "base.entity_id", "count_method": "value_count", "order": "desc", "percentage": "no"},
        group_fields=[{"field": "asset_base.businesses.name", "limit": 10}],
        f_category="group",
        metric_type="group",
        s_category="group",
    )

    os_chart_id = creat_chart(
        chart_name="操作系统分布",
        chart_type="os",
        category="asset",
        count_info={"field": "base.entity_id", "count_method": "value_count", "order": "desc", "percentage": "no"},
        group_fields=[{"field": "computer.os.distribution", "limit": 10}, {"field": None, "limit": 10}],
        f_category="group",
        metric_type="group",
        s_category="group",
    )

    asset_type_id = creat_chart(
        chart_name="资产类型分布",
        chart_type="treemap",
        category="asset",
        count_info={"field": "base.entity_id", "count_method": "value_count", "order": "desc", "percentage": "no"},
        group_fields=[{"field": "base.asset_type_display_name", "limit": 10}, {"field": None, "limit": 10}],
        f_category="group",
        metric_type="group",
    )

    software_id = creat_chart(
        chart_name="安装软件Top10",
        chart_type="bar",
        category="overView",
        count_info={"field": "base.entity_id", "count_method": "value_count", "order": "desc", "percentage": "no"},
        group_fields=[{"field": "computer.softwares.name", "limit": 10}, {"field": None, "limit": 10}],
        f_category="group",
        metric_type="group",
        s_category="group",
    )

    table_asset_chart_id = creat_chart(
        chart_name="无归属资产",
        metric_type="table",
        chart_type="table",
        category="asset",
        base_query="not $.asset_base.owners.exists() and $.network.priority_addr.exists()",
        table={"field_list": [{"value": "network.priority_addr"}, {"value": "base.asset_type_display_name"}]},
    )

    host_loophole_id = creat_chart(
        chart_name="漏洞数量统计排行Top10",
        chart_type="histogram",
        group_fields=[{"field": "network.priority_addr", "limit": 10}, {"field": None, "limit": 10}],
        count_info={
            "field": "vulnerability.vulners.vul_id",
            "count_method": "value_count",
            "order": "desc",
            "percentage": "yes",
        },
        s_category="group",
        metric_type="group",
        f_category="group",
        category="asset",
        base_query="",
    )
    create_space(
        name="资产融合",
        index=3,
        chart_info=[
            {
                "chart": str(host_asset_id),
                "chart_instance_name": "主机资产",
                "horizontal_index": 2,
                "vertical_index": 0,
                "width": 1,
                "height": 1,
            },
            {
                "chart": str(adapter_chart_id),
                "chart_instance_name": "资产发现",
                "horizontal_index": 0,
                "vertical_index": 0,
                "width": 2,
                "height": 5,
            },
            {
                "chart": str(docker_id),
                "chart_instance_name": "容器",
                "horizontal_index": 7,
                "vertical_index": 0,
                "width": 1,
                "height": 1,
            },
            {
                "chart": str(asset_type_id),
                "chart_instance_name": "资产类型分布",
                "horizontal_index": 0,
                "vertical_index": 5,
                "width": 2,
                "height": 4,
            },
            {
                "chart": str(business_chart_id),
                "chart_instance_name": "业务资产数量Top10",
                "horizontal_index": 2,
                "vertical_index": 1,
                "width": 2,
                "height": 4,
            },
            {
                "chart": str(port_service_id),
                "chart_instance_name": "端口服务",
                "horizontal_index": 5,
                "vertical_index": 0,
                "width": 1,
                "height": 1,
            },
            {
                "chart": str(domain_id),
                "chart_instance_name": "域名",
                "horizontal_index": 4,
                "vertical_index": 0,
                "width": 1,
                "height": 1,
            },
            {
                "chart": str(edr_asset_id),
                "chart_instance_name": "终端资产",
                "horizontal_index": 3,
                "vertical_index": 0,
                "width": 1,
                "height": 1,
            },
            {
                "chart": str(os_chart_id),
                "chart_instance_name": "操作系统分布",
                "horizontal_index": 4,
                "vertical_index": 1,
                "width": 2,
                "height": 4,
            },
            {
                "chart": str(table_asset_chart_id),
                "chart_instance_name": "无归属资产",
                "horizontal_index": 4,
                "vertical_index": 5,
                "width": 4,
                "height": 4,
            },
            {
                "chart": str(software_id),
                "chart_instance_name": "安装软件Top10",
                "horizontal_index": 6,
                "vertical_index": 1,
                "width": 2,
                "height": 4,
            },
            {
                "chart": str(host_loophole_id),
                "chart_instance_name": "漏洞数量统计排行Top10",
                "horizontal_index": 2,
                "vertical_index": 5,
                "width": 2,
                "height": 4,
            },
        ],
    )


def careate_asset_vul_instance():
    serious_id = creat_chart(
        chart_name="紧急程度:严重",
        category="asset_vul_instance",
        chart_type="asset_vul_instance_histogram",
        metric_type="asql",
        asql={
            "querys": [
                {
                    "value": "",
                    "label": "",
                    "aql": '$.asset_vul_instance.severity = "严重"',
                    "type": "asql",
                },
                {
                    "value": "",
                    "label": "",
                    "aql": '$.asset_vul_instance.remediation_severity = "严重"',
                    "type": "asql",
                },
            ]
        },
    )
    high_id = creat_chart(
        chart_name="紧急程度:高",
        category="asset_vul_instance",
        metric_type="asql",
        chart_type="asset_vul_instance_histogram",
        asql={
            "querys": [
                {
                    "value": "",
                    "label": "",
                    "aql": '$.asset_vul_instance.severity = "高危"',
                    "type": "asql",
                },
                {
                    "value": "",
                    "label": "",
                    "aql": '$.asset_vul_instance.remediation_severity = "高"',
                    "type": "asql",
                },
            ]
        },
    )

    middle_id = creat_chart(
        chart_name="紧急程度:中",
        category="asset_vul_instance",
        chart_type="asset_vul_instance_histogram",
        metric_type="asql",
        asql={
            "querys": [
                {
                    "value": "",
                    "label": "",
                    "aql": '$.asset_vul_instance.severity = "中危"',
                    "type": "asql",
                },
                {
                    "value": "",
                    "label": "",
                    "aql": '$.asset_vul_instance.remediation_severity = "中"',
                    "type": "asql",
                },
            ]
        },
    )

    low_id = creat_chart(
        chart_name="紧急程度:低",
        category="asset_vul_instance",
        chart_type="asset_vul_instance_histogram",
        metric_type="asql",
        asql={
            "querys": [
                {
                    "value": "",
                    "label": "",
                    "aql": '$.asset_vul_instance.severity = "低危"',
                    "type": "asql",
                },
                {
                    "value": "",
                    "label": "",
                    "aql": '$.asset_vul_instance.remediation_severity = "低"',
                    "type": "asql",
                },
            ]
        },
    )

    None_id = creat_chart(
        chart_name="紧急程度:无",
        category="asset_vul_instance",
        chart_type="asset_vul_instance_histogram",
        metric_type="asql",
        asql={
            "querys": [
                {
                    "value": "",
                    "label": "",
                    "aql": '$.asset_vul_instance.severity = "无"',
                    "type": "asql",
                },
                {
                    "value": "",
                    "label": "",
                    "aql": '$.asset_vul_instance.remediation_severity = "无"',
                    "type": "asql",
                },
            ]
        },
    )

    meta_view_service.update_chart_by_category(
        category="asset_vul_instance", value={"charts": [serious_id, high_id, middle_id, low_id, None_id]}
    )


POINT = DashboardInitializer
