from caasm_charts_manage.charts_manage.builder.build_count_chart import B<PERSON><PERSON>ount<PERSON><PERSON>
from caasm_charts_manage.charts_manage.builder.chart_builder import ChartBuilder
from caasm_charts_manage.charts_manage.util.enum import MetricType
from caasm_render.runtime import render_manager
from caasm_service.entity.meta_model import MetaFieldType
from caasm_service.runtime import adapter_service
from caasm_tool.util import extract


class BuildGroupCountChart(BuildCountChart):
    _ADAPTER = "base.adapters"

    def __init__(self, category=None, base_query=None, base_date=None):
        super(BuildGroupCountChart, self).__init__(category=category, base_query=base_query, base_date=base_date)
        self.group_fields = None
        self.f_field = None
        self.search_f_field = None
        self.f_limit = None
        self.f_limit_field = None

        self.s_field = None
        self.search_s_field = None
        self.s_limit = None
        self.s_limit_field = None
        self.time_limit = None
        self.meta_field_mapper = None

        self.f_category = None
        self.s_category = None

        self.aql_filter_statement = {"bool": {"must": [{"bool": {"must": []}}]}}

    def parser_kwargs(self, kwargs=None):
        super(BuildGroupCountChart, self).parser_kwargs(kwargs)
        self.group_fields = kwargs.get("group_fields")
        self.f_category = kwargs.get("f_category")
        self.s_category = kwargs.get("s_category")
        if self.group_fields and isinstance(self.group_fields, list):
            if 1 == len(self.group_fields):
                self.f_field, self.f_limit, self.f_limit_field = self.resign_field_info(self.group_fields[0])
            if 2 == len(self.group_fields):
                self.f_field, self.f_limit, self.f_limit_field = self.resign_field_info(self.group_fields[0])
                self.s_field, self.s_limit, self.s_limit_field = self.resign_field_info(self.group_fields[1])
        else:
            if self.f_category != MetricType.TIME.value and self.s_category != MetricType.COUNT.value:
                raise ValueError("计数类型错误！")
        if self.f_category == MetricType.TIME.value:
            self.time_limit = extract(kwargs, "time.limit")

    def make_chart(self, kwargs):
        """
        这里会有两个逻辑 ，分别执行两次，其中一个是 查分母，一个是查分子
        """
        self.parser_kwargs(kwargs)
        self.check_field_validity()
        self.build_search_index()
        return self.make_default_chart()

    def make_default_chart(self):
        query = self.build_search_query(query=self.base_query)
        data = self.execute(query=query)
        temp_result = self.clean_search_result(data)
        self.result = self.make_data_category(temp_result)
        return self.result

    def make_data_category(self, data):
        result = []
        if not len(data):
            return result

        result = data

        if self.time_limit and len(data) > self.time_limit:
            result = data[0 : self.time_limit]

        if "category" in result[0] and self.time_limit:
            result = self.handle_unknown_field(result)

        if not self.time_limit and isinstance(result[0], dict) and not result[0].get("category"):
            if self.order == "desc":
                if self.proportion_flag:
                    result = sorted(result, key=lambda x: x["proportion_count"], reverse=True)
                else:
                    result = sorted(result, key=lambda x: x["count"], reverse=True)
            else:
                if self.proportion_flag:
                    result = sorted(result, key=lambda x: x["proportion_count"])
                result = sorted(result, key=lambda x: x["count"])
        return result

    def handle_unknown_field(self, data):
        all_field = []

        for item in data:
            for field in item.get("category", []):
                name = field.get("name")
                if not field.get("name"):
                    continue
                if name not in all_field:
                    all_field.append(name)
        temp_data = []
        for item in data:
            _temp_fields = []
            for field in item.get("category", []):
                name = field.get("name")
                if name:
                    _temp_fields.append(name)
            other_data = list(set(all_field) - set(_temp_fields))

            for _field in other_data:
                item["category"].append({"name": _field, "count": 0, "asql": ""})
            if self.order == "desc":
                item["category"] = sorted(item["category"], key=lambda x: x["name"], reverse=True)
            else:
                item["category"] = sorted(item["category"], key=lambda x: x["name"])

        return data

    def clean_search_result(self, source_result):
        base_builder = ChartBuilder(category=self.category, base_date=self.base_date, base_query=self.base_query)
        data = base_builder.clean_search_result(source_result=source_result)
        if self.f_field:
            # 这里指的是 查询有几个查询结果
            f_meta_field = self.meta_field_mapper.get(self.f_field)

            ## 如果首字母 是复杂的，并且没有与时间想过的
            if f_meta_field.complex_full_name and not self.time:
                result = self.handle_f_complex_and_not_time(data, f_meta_field)

            elif f_meta_field.complex_full_name and self.time and self.f_category == MetricType.TIME.value:
                result = self.handle_f_complex_and_f_time(data)

            elif f_meta_field.complex_full_name and self.time and self.s_category == MetricType.TIME.value:
                result = self.handle_f_complex_and_s_time(data)

            elif not f_meta_field.complex_full_name and self.time and self.f_category == MetricType.TIME.value:
                result = self.handle_n_complex_and_f_time(data)

            elif not f_meta_field.complex_full_name and self.time and self.s_category == MetricType.TIME.value:
                result = self.handle_n_complex_and_s_time(data)
            else:
                result = self.handle_n_complex_and_not_time(data)
        else:
            result = self.handle_time_and_count(data)
        if self.f_field == self._ADAPTER:
            adapter_data = adapter_service.find_adapter(fields=["name", "display_name"])
            adapter_mapper = {}
            for adapter in adapter_data:
                adapter_mapper[adapter.name] = adapter.display_name
            for _single_result in result:
                if self.time:
                    adapter_names = _single_result["category"]
                    if adapter_names:
                        for adapter_name in adapter_names:
                            adapter_name["name"] = adapter_mapper.get(
                                adapter_name.get("name"), adapter_name.get("name")
                            )
                else:
                    _single_result["name"] = adapter_mapper.get(_single_result["name"], _single_result["name"])
        return result

    def handle_time_and_count(self, data):
        result = []

        unknown_index = 0
        for i in range(0, len(self.time_display_group)):
            temp = []

            if self.date_group[i] in self.no_es_index_data:
                unknown_index += 1
                temp.append({"name": self.time_display_group[i], "count": 0})
                result.extend(temp)
                continue

            aggregations = data[i - unknown_index].get("aggregations", {})

            if not aggregations:
                return []

            count_meta_field = self.meta_field_mapper.get(self.count_field)
            if count_meta_field.complex_full_name:
                total = extract(aggregations, "count.count.value")
                filtered_count = extract(aggregations, "filtered_count.count.count.value")
                _temp_data = {
                    "name": self.time_display_group[i],
                    "count": extract(aggregations, "count.count.value"),
                    "proportion_count": (
                        round((filtered_count / total) * 100, 2) if self.proportion_flag and total != 0 else 0
                    ),
                    "is_percentage": self.proportion_flag,
                }
            else:
                total = extract(aggregations, "count.value")
                filtered_count = extract(aggregations, "filtered_count.count.value")
                _temp_data = {
                    "name": self.time_display_group[i],
                    "count": extract(aggregations, "count.value"),
                    "proportion_count": (
                        round((filtered_count / total) * 100, 2) if self.proportion_flag and total != 0 else 0
                    ),
                    "is_percentage": self.proportion_flag,
                }

            temp.append(_temp_data)
            result.extend(temp)
        return result

    def handle_n_complex_and_not_time(self, data):
        result = []
        for item in data:
            temp = []
            aggregations = item.get("aggregations", {})
            if not aggregations:
                return []

            f_groups = extract(aggregations, "f_group.buckets")
            for f_group in f_groups:
                f_display_name = (
                    round(f_group.get("key"), 2) if isinstance(f_group.get("key"), float) else f_group.get("key")
                )
                _temp_data = {"name": f_display_name}

                if self.s_field:
                    s_meta_field = self.meta_field_mapper.get(self.s_field)
                    if s_meta_field.complex_full_name:
                        s_groups = extract(f_group, "s_group.s_group.buckets")
                        category = []
                        for s_group in s_groups:
                            s_display_name = (
                                round(s_group.get("key"), 2)
                                if isinstance(s_group.get("key"), float)
                                else s_group.get("key")
                            )
                            count_meta_field = self.meta_field_mapper.get(self.count_field)
                            if count_meta_field.complex_full_name:
                                filtered_count = extract(
                                    s_group, "reverse_nested_aggs.count.filtered_count._count.value"
                                )
                                total = extract(s_group, "reverse_nested_aggs.count._count.value")
                                category.append(
                                    {
                                        "name": s_display_name,
                                        "count": extract(s_group, "reverse_nested_aggs.count._count.value"),
                                        "asql": self.get_asql(
                                            base_query=self.base_query,
                                            f_field=self.f_field,
                                            f_value=f_display_name,
                                            filter_asql=self.asql_filter,
                                            s_field=self.s_field,
                                            s_value=s_display_name,
                                        ),
                                        "proportion_count": (
                                            round((filtered_count / total) * 100, 2)
                                            if self.proportion_flag and total != 0
                                            else 0
                                        ),
                                        "is_percentage": self.proportion_flag,
                                    }
                                )
                            else:
                                filtered_count = extract(s_group, "reverse_nested_aggs.filtered_count._count.value")
                                total = extract(s_group, "reverse_nested_aggs._count.value")
                                category.append(
                                    {
                                        "name": s_display_name,
                                        "count": total,
                                        "asql": self.get_asql(
                                            base_query=self.base_query,
                                            f_field=self.f_field,
                                            f_value=f_display_name,
                                            filter_asql=self.asql_filter,
                                            s_field=self.s_field,
                                            s_value=s_display_name,
                                        ),
                                        "filtered_count": filtered_count,
                                        "proportion_count": (
                                            round((filtered_count / total) * 100, 2)
                                            if self.proportion_flag and total != 0
                                            else 0
                                        ),
                                        "is_percentage": self.proportion_flag,
                                    }
                                )
                        _temp_data.update({"category": category})
                    else:
                        s_groups = extract(f_group, "s_group.buckets")
                        category = []
                        count_meta_field = self.meta_field_mapper.get(self.count_field)
                        if count_meta_field.complex_full_name:
                            for s_group in s_groups:
                                s_display_name = (
                                    round(s_group.get("key"), 2)
                                    if isinstance(s_group.get("key"), float)
                                    else s_group.get("key")
                                )
                                total = extract(s_group, "count.count.value")
                                filtered_count = extract(f_group, "count.filtered_count.count.count.value")
                                category.append(
                                    {
                                        "name": s_display_name,
                                        "count": extract(s_group, "count.count.value"),
                                        "asql": self.get_asql(
                                            base_query=self.base_query,
                                            f_field=self.f_field,
                                            f_value=f_display_name,
                                            filter_asql=self.asql_filter,
                                            s_field=self.s_field,
                                            s_value=s_display_name,
                                        ),
                                        "proportion_count": (
                                            round((filtered_count / total) * 100, 2)
                                            if self.proportion_flag and total != 0
                                            else 0
                                        ),
                                        "is_percentage": self.proportion_flag,
                                    }
                                )
                        else:
                            for s_group in s_groups:
                                s_display_name = (
                                    round(s_group.get("key"), 2)
                                    if isinstance(s_group.get("key"), float)
                                    else s_group.get("key")
                                )
                                total = extract(s_group, "count.value")
                                filtered_count = extract(s_group, "filtered_count.count.value")
                                category.append(
                                    {
                                        "name": s_display_name,
                                        "count": extract(s_group, "count.value"),
                                        "asql": self.get_asql(
                                            base_query=self.base_query,
                                            f_field=self.f_field,
                                            f_value=f_display_name,
                                            filter_asql=self.asql_filter,
                                            s_field=self.s_field,
                                            s_value=s_display_name,
                                        ),
                                        "proportion_count": (
                                            round((filtered_count / total) * 100, 2)
                                            if self.proportion_flag and total != 0
                                            else 0
                                        ),
                                        "is_percentage": self.proportion_flag,
                                    }
                                )
                        _temp_data.update({"category": category})

                else:
                    count_meta_field = self.meta_field_mapper.get(self.count_field)
                    if count_meta_field.complex_full_name:
                        filtered_count = extract(f_group, "nested_counts.filtered_count.doc_count")
                        total = extract(f_group, "nested_counts.total_count.value")
                    else:
                        filtered_count = extract(f_group, "filtered_count.count.value")
                        total = extract(f_group, "count")

                    _temp_data.update(
                        {
                            "count": filtered_count,
                            "asql": self.get_asql(
                                base_query=self.base_query,
                                f_field=self.f_field,
                                f_value=f_display_name,
                                filter_asql=self.asql_filter,
                            ),
                            "proportion_count": (
                                round(filtered_count / total * 100, 2) if self.proportion_flag and total != 0 else 0
                            ),
                            "is_percentage": self.proportion_flag,
                        }
                    )
                temp.append(_temp_data)
            result.extend(temp)
        return result

    def handle_n_complex_and_s_time(self, data):

        f_category_arr = []
        for item in data:
            aggregations = item.get("aggregations", {})
            if not aggregations:
                return []
            f_groups = extract(aggregations, "f_group.buckets")
            for f_group in f_groups:
                f_display_name = (
                    round(f_group.get("key"), 2) if isinstance(f_group.get("key"), float) else f_group.get("key")
                )
                if f_display_name in f_category_arr:
                    continue
                else:
                    f_category_arr.append(f_display_name)
        result = []
        for field in f_category_arr:
            _temp_data = {"name": field}
            category = []
            for i in range(0, len(self.time_display_group)):
                aggregations = data[i].get("aggregations", {})
                if not aggregations:
                    return []
                f_groups = extract(aggregations, "f_group.buckets")
                for f_group in f_groups:
                    f_display_name = (
                        round(f_group.get("key"), 2) if isinstance(f_group.get("key"), float) else f_group.get("key")
                    )
                    count_meta_field = self.meta_field_mapper.get(self.count_field)
                    if count_meta_field.complex_full_name:
                        if f_display_name != field:
                            continue
                        total = extract(f_group, "count.count.value")
                        filtered_count = extract(f_group, "count.filtered_count.count.count.value")
                        category.append(
                            {
                                "asql": self.get_asql(
                                    base_query=self.base_query,
                                    f_field=self.f_field,
                                    f_value=f_display_name,
                                    filter_asql=self.asql_filter,
                                ),
                                "name": self.time_display_group[i],
                                "count": extract(f_group, "count.count.value"),
                                "proportion_count": (
                                    round((filtered_count / total) * 100, 2)
                                    if self.proportion_flag and total != 0
                                    else 0
                                ),
                                "is_percentage": self.proportion_flag,
                            }
                        )

                    else:
                        if f_display_name != field:
                            continue
                        total = extract(f_group, "count.value")
                        filtered_count = extract(f_group, "filtered_count.count.value")
                        category.append(
                            {
                                "asql": self.get_asql(
                                    base_query=self.base_query,
                                    f_field=self.f_field,
                                    f_value=f_display_name,
                                    filter_asql=self.asql_filter,
                                ),
                                "name": self.time_display_group[i],
                                "count": extract(f_group, "count.value"),
                                "proportion_count": (
                                    round((filtered_count / total) * 100, 2)
                                    if self.proportion_flag and total != 0
                                    else 0
                                ),
                                "is_percentage": self.proportion_flag,
                            }
                        )
            _temp_data["category"] = category
            result.append(_temp_data)
        return result

    def handle_n_complex_and_f_time(self, data):
        result = []

        unknown_index = 0
        count_meta_field = self.meta_field_mapper.get(self.count_field)
        for i in range(0, len(self.time_display_group)):
            temp = []
            if self.date_group[i] in self.no_es_index_data:
                unknown_index += 1
                if count_meta_field.complex_full_name:
                    temp.append({"name": self.time_display_group[i], "category": []})
                else:
                    temp.append({"name": self.time_display_group[i], "category": []})

                result.extend(temp)
                continue

            _temp_data = {"name": self.time_display_group[i]}
            aggregations = data[i - unknown_index].get("aggregations", {})
            if not aggregations:
                return []

            f_groups = extract(aggregations, "f_group.buckets")
            category = []
            for f_group in f_groups:
                f_display_name = (
                    round(f_group.get("key"), 2) if isinstance(f_group.get("key"), float) else f_group.get("key")
                )
                if count_meta_field.complex_full_name:
                    total = extract(f_group, "reverse_nested_aggs.reverse_nested_aggs.count.count.value")
                    filtered_count = extract(
                        f_group, "reverse_nested_aggs.reverse_nested_aggs.filtered_count.count.count.value"
                    )
                    category.append(
                        {
                            "name": f_display_name,
                            "count": total,
                            "asql": self.get_asql(
                                base_query=self.base_query,
                                f_field=self.f_field,
                                f_value=f_display_name,
                                filter_asql=self.asql_filter,
                            ),
                            "proportion_count": (
                                round((filtered_count / total) * 100, 2) if self.proportion_flag and total != 0 else 0
                            ),
                            "is_percentage": self.proportion_flag,
                        }
                    )
                else:
                    total = extract(f_group, "count.value")
                    filtered_count = extract(f_group, "filtered_count.count.value")
                    category.append(
                        {
                            "name": f_display_name,
                            "count": total,
                            "asql": self.get_asql(
                                base_query=self.base_query,
                                f_field=self.f_field,
                                f_value=f_display_name,
                                filter_asql=self.asql_filter,
                            ),
                            "proportion_count": (
                                round((filtered_count / total) * 100, 2) if self.proportion_flag and total != 0 else 0
                            ),
                            "is_percentage": self.proportion_flag,
                        }
                    )
            _temp_data.update({"category": category})
            temp.append(_temp_data)
            result.extend(temp)
        return result

    def handle_f_complex_and_s_time(self, data):
        result = []
        for i in range(0, len(data)):
            temp = []
            aggregations = data[i].get("aggregations", {})
            if not aggregations:
                return []

            f_groups = extract(aggregations, "f_group.f_group.buckets")
            category = []
            for f_group in f_groups:
                f_display_name = (
                    round(f_group.get("key"), 2) if isinstance(f_group.get("key"), float) else f_group.get("key")
                )
                _temp_data = {"name": f_group.get("key")}
                count_meta_field = self.meta_field_mapper.get(self.count_field)
                if count_meta_field.complex_full_name:
                    total = extract(f_group, "reverse_nested_aggs.reverse_nested_aggs.count.count.value")
                    filtered_count = extract(
                        f_group, "reverse_nested_aggs.reverse_nested_aggs.filtered_count.count.count.value"
                    )
                    category.append(
                        {
                            "name": self.time_display_group[i],
                            "count": extract(f_group, "reverse_nested_aggs.reverse_nested_aggs.count.count.value"),
                            "asql": self.get_asql(
                                base_query=self.base_query,
                                f_field=self.f_field,
                                f_value=f_display_name,
                                filter_asql=self.asql_filter,
                            ),
                            "proportion_count": (
                                round((filtered_count / total) * 100, 2) if self.proportion_flag and total != 0 else 0
                            ),
                            "is_percentage": self.proportion_flag,
                        }
                    )
                else:
                    total = extract(f_group, "reverse_nested_aggs.reverse_nested_aggs.count.value")
                    filtered_count = extract(
                        f_group, "reverse_nested_aggs.reverse_nested_aggs.filtered_count.count.value"
                    )
                    category.append(
                        {
                            "name": self.time_display_group[i],
                            "count": extract(f_group, "reverse_nested_aggs.reverse_nested_aggs.count.value"),
                            "asql": self.get_asql(
                                base_query=self.base_query,
                                f_field=self.f_field,
                                f_value=f_display_name,
                                filter_asql=self.asql_filter,
                            ),
                            "proportion_count": (
                                round((filtered_count / total) * 100, 2) if self.proportion_flag and total != 0 else 0
                            ),
                            "is_percentage": self.proportion_flag,
                        }
                    )
                _temp_data.update({"category": category})
                temp.append(_temp_data)
            result.extend(temp)
        return result

    def handle_f_complex_and_f_time(self, data):
        result = []

        count_meta_field = self.meta_field_mapper.get(self.count_field)
        unknown_index = 0
        for i in range(0, len(self.time_display_group)):
            temp = []
            if self.date_group[i] in self.no_es_index_data:
                unknown_index += 1
                if count_meta_field.complex_full_name:

                    temp.append({"name": self.time_display_group[i], "category": []})
                else:
                    temp.append({"name": self.time_display_group[i], "category": []})
                result.extend(temp)

                continue

            _temp_data = {"name": self.time_display_group[i]}
            aggregations = data[i - unknown_index].get("aggregations", {})

            if not aggregations:
                return []

            f_groups = extract(aggregations, "f_group.f_group.buckets")
            category = []
            for f_group in f_groups:
                f_display_name = (
                    round(f_group.get("key"), 2) if isinstance(f_group.get("key"), float) else f_group.get("key")
                )

                if count_meta_field.complex_full_name:
                    total = extract(f_group, "reverse_nested_aggs.count._count.value")
                    filtered_count = extract(f_group, "reverse_nested_aggs.filtered_count._count.value")

                    category.append(
                        {
                            "name": f_display_name,
                            "count": total,
                            "asql": self.get_asql(
                                base_query=self.base_query,
                                f_field=self.f_field,
                                f_value=f_display_name,
                                filter_asql=self.asql_filter,
                            ),
                            "proportion_count": (
                                round((filtered_count / total) * 100, 2) if self.proportion_flag and total != 0 else 0
                            ),
                            "is_percentage": self.proportion_flag,
                        }
                    )
                else:
                    total = extract(f_group, "reverse_nested_aggs._count.value")
                    filtered_count = extract(f_group, "reverse_nested_aggs.filtered_count._count.value")

                    category.append(
                        {
                            "name": f_display_name,
                            "count": extract(f_group, "reverse_nested_aggs._count.value"),
                            "asql": self.get_asql(
                                base_query=self.base_query,
                                f_field=self.f_field,
                                f_value=f_display_name,
                                filter_asql=self.asql_filter,
                            ),
                            "proportion_count": (
                                round((filtered_count / total) * 100, 2) if self.proportion_flag and total != 0 else 0
                            ),
                            "is_percentage": self.proportion_flag,
                        }
                    )
            _temp_data.update({"category": category})
            temp.append(_temp_data)
            result.extend(temp)
        return result

    def get_asql(self, base_query=None, f_field=None, f_value=None, s_field=None, s_value=None, filter_asql=None):
        parts = []
        if base_query:
            parts.append(base_query)
        if f_field and f_value is not None:
            parts.append(f'$.{f_field} = "{f_value}"')
        if s_field and s_value is not None:
            parts.append(f'$.{s_field} = "{s_value}"')
        if filter_asql:
            parts.append(filter_asql)
        return ";".join(parts) if parts else ""

    def handle_f_complex_and_not_time(self, data, f_meta_field):
        result = []

        for item in data:
            temp = []
            aggregations = item.get("aggregations", {})

            if not aggregations:
                return []
            f_groups = extract(aggregations, "f_group.f_group.buckets")

            for f_group in f_groups:
                f_display_name = (
                    round(f_group.get("key"), 2) if isinstance(f_group.get("key"), float) else f_group.get("key")
                )

                _temp_data = {"name": f_group.get("key")}
                if self.s_field:
                    s_meta_field = self.meta_field_mapper.get(self.s_field)
                    if s_meta_field.complex_full_name:
                        s_groups = extract(f_group, "reverse_nested_aggs.s_group.s_group.buckets")
                        category = []
                        count_meta_field = self.meta_field_mapper.get(self.count_field)
                        if count_meta_field.complex_full_name:
                            for s_group in s_groups:
                                s_display_name = (
                                    round(s_group.get("key"), 2)
                                    if isinstance(s_group.get("key"), float)
                                    else s_group.get("key")
                                )
                                total = extract(s_group, "reverse_nested_aggs.count._count.value")
                                filtered_count = extract(
                                    s_group, "reverse_nested_aggs.count.filtered_count._count.value"
                                )
                                category.append(
                                    {
                                        "name": s_display_name,
                                        "asql": self.get_asql(
                                            base_query=self.base_query,
                                            f_field=self.f_field,
                                            f_value=f_display_name,
                                            filter_asql=self.asql_filter,
                                            s_field=self.s_field,
                                            s_value=s_display_name,
                                        ),
                                        "count": total,
                                        "proportion_count": (
                                            round((filtered_count / total) * 100, 2)
                                            if self.proportion_flag and total != 0
                                            else 0
                                        ),
                                        "is_percentage": self.proportion_flag,
                                    }
                                )

                        else:
                            for s_group in s_groups:
                                s_display_name = (
                                    round(s_group.get("key"), 2)
                                    if isinstance(s_group.get("key"), float)
                                    else s_group.get("key")
                                )
                                total = extract(s_group, "reverse_nested_aggs._count.value")
                                filtered_count = extract(s_group, "reverse_nested_aggs.filtered_count._count.value")
                                category.append(
                                    {
                                        "name": s_display_name,
                                        "asql": self.get_asql(
                                            base_query=self.base_query,
                                            f_field=self.f_field,
                                            f_value=f_display_name,
                                            filter_asql=self.asql_filter,
                                            s_field=self.s_field,
                                            s_value=s_display_name,
                                        ),
                                        "count": total,
                                        "proportion_count": (
                                            round((filtered_count / total) * 100, 2)
                                            if self.proportion_flag and total != 0
                                            else 0
                                        ),
                                        "is_percentage": self.proportion_flag,
                                    }
                                )
                        _temp_data.update({"category": category})

                    else:
                        s_groups = extract(f_group, "reverse_nested_aggs.s_group.buckets")
                        category = []
                        count_meta_field = self.meta_field_mapper.get(self.count_field)
                        if count_meta_field.complex_full_name:
                            for s_group in s_groups:
                                s_display_name = (
                                    round(s_group.get("key"), 2)
                                    if isinstance(s_group.get("key"), float)
                                    else s_group.get("key")
                                )
                                total = extract(s_group, "reverse_nested_aggs.count._count.value")
                                filtered_count = extract(
                                    s_group, "reverse_nested_aggs.filtered_count.count._count.value"
                                )
                                category.append(
                                    {
                                        "name": s_display_name,
                                        "asql": self.get_asql(
                                            base_query=self.base_query,
                                            f_field=self.f_field,
                                            f_value=f_display_name,
                                            filter_asql=self.asql_filter,
                                            s_field=self.s_field,
                                            s_value=s_display_name,
                                        ),
                                        "count": extract(s_group, "reverse_nested_aggs.count._count.value"),
                                        "proportion_count": (
                                            round((filtered_count / total) * 100, 2)
                                            if self.proportion_flag and total != 0
                                            else 0
                                        ),
                                        "is_percentage": self.proportion_flag,
                                    }
                                )

                        else:
                            for s_group in s_groups:
                                s_display_name = (
                                    round(s_group.get("key"), 2)
                                    if isinstance(s_group.get("key"), float)
                                    else s_group.get("key")
                                )
                                total = extract(s_group, "reverse_nested_aggs._count.value")
                                filtered_count = extract(s_group, "reverse_nested_aggs.filtered_count._count.value")

                                category.append(
                                    {
                                        "name": s_display_name,
                                        "asql": self.get_asql(
                                            base_query=self.base_query,
                                            f_field=self.f_field,
                                            f_value=f_display_name,
                                            filter_asql=self.asql_filter,
                                            s_field=self.s_field,
                                            s_value=s_display_name,
                                        ),
                                        "count": extract(s_group, "reverse_nested_aggs._count.value"),
                                        "proportion_count": (
                                            round((filtered_count / total) * 100, 2)
                                            if self.proportion_flag and total != 0
                                            else 0
                                        ),
                                        "is_percentage": self.proportion_flag,
                                    }
                                )
                        _temp_data.update({"category": category})
                    temp.append(_temp_data)

                else:
                    count_meta_field = self.meta_field_mapper.get(self.count_field)

                    if count_meta_field.complex_full_name:
                        total = extract(f_group, "reverse_nested_aggs.count._count.value")
                        filtered_count = extract(f_group, "reverse_nested_aggs.count.filtered_count._count.value")

                        _temp_data.update(
                            {
                                "count": filtered_count if self.proportion_flag else total,
                                "asql": self.get_asql(
                                    base_query=self.base_query,
                                    f_field=self.f_field,
                                    f_value=f_display_name,
                                    filter_asql=self.asql_filter,
                                ),
                                "proportion_count": (
                                    round((filtered_count / total) * 100, 2)
                                    if self.proportion_flag and total != 0
                                    else 0
                                ),
                                "is_percentage": self.proportion_flag,
                            }
                        )
                    else:
                        total = extract(f_group, "reverse_nested_aggs._count.value")
                        filtered_count = extract(f_group, "reverse_nested_aggs.filtered_count._count.value")

                        _temp_data.update(
                            {
                                "count": filtered_count if self.proportion_flag else total,
                                "asql": self.get_asql(
                                    base_query=self.base_query,
                                    f_field=self.f_field,
                                    f_value=f_display_name,
                                    filter_asql=self.asql_filter,
                                ),
                                "proportion_count": (
                                    round((filtered_count / total) * 100, 2)
                                    if self.proportion_flag and total != 0
                                    else 0
                                ),
                                "is_percentage": self.proportion_flag,
                            }
                        )
                    temp.append(_temp_data)
            result.extend(temp)

        return result

    def check_field_validity(self):
        super(BuildGroupCountChart, self).check_field_validity()

        # if not self.f_field:
        #     raise ValueError(f"not find field in chart Config!")

    def resign_field_info(self, param):
        return param.get("field"), param.get("limit"), param.get("limit_field")

    def build_base_query(self, query=None):
        base_builder = ChartBuilder(category=self.category, base_query=query, base_date=self.base_date)
        return base_builder.build_search_query(query=query)

    def build_search_query(self, query=None):
        result = self.build_base_query(query=query)
        self.meta_field_mapper = render_manager.default_query.find_field_to_mapper(self.category)
        f_meta_field = self.meta_field_mapper.get(self.f_field)
        count_meta_field = self.meta_field_mapper.get(self.count_field)
        if not count_meta_field:
            return result
        if count_meta_field.type == MetaFieldType.ENUM:
            self.search_count_field = f"{self.count_field}.text"
        else:
            self.search_count_field = self.count_field

        if self.asql_filter:
            self.aql_filter_statement = self.parse_aql(self.asql_filter, self.category)
        if self.f_field:
            if not f_meta_field:
                return result
            if f_meta_field.type == MetaFieldType.ENUM:
                self.search_f_field = f"{self.f_field}.text"
            else:
                self.search_f_field = self.f_field
            if f_meta_field.complex_full_name:
                if self.s_field:
                    s_meta_field = self.meta_field_mapper.get(self.s_field)
                    if not s_meta_field:
                        return result
                    if s_meta_field.type == MetaFieldType.ENUM:
                        self.search_s_field = f"{self.s_field}.text"
                    else:
                        self.search_s_field = self.s_field
                    query = self.build_f_s_c_query(f_meta_field, s_meta_field, count_meta_field)
                else:
                    query = self.build_f_c_query(f_meta_field, count_meta_field)
            else:
                if self.s_field:
                    s_meta_field = self.meta_field_mapper.get(self.s_field)
                    if not s_meta_field:
                        return result
                    if s_meta_field.type == MetaFieldType.ENUM:
                        self.search_s_field = f"{self.s_field}.text"
                    else:
                        self.search_s_field = self.s_field
                    query = self.build_simple_f_s_c_query(f_meta_field, s_meta_field, count_meta_field)
                else:
                    query = self.build_simple_f_c_query(f_meta_field, count_meta_field)
            result.update(query)
        else:
            if not count_meta_field:
                return result
            query = self.build_c_query(count_meta_field)
            result.update(query)
        return result

    def build_f_c_query(self, f_meta_field, count_meta_field):
        if count_meta_field.complex_full_name:
            query = {
                "aggs": {
                    "f_group": {
                        "nested": {"path": f_meta_field.complex_full_name},
                        "aggs": {
                            "f_group": {
                                "terms": {"field": self.f_field, "size": self.f_limit, "order": {"_count": self.order}},
                                "aggs": {
                                    "reverse_nested_aggs": {
                                        "reverse_nested": {},
                                        "aggs": {
                                            "count": {
                                                "nested": {"path": count_meta_field.complex_full_name},
                                                "aggs": {
                                                    "_count": {self.count_method: {"field": self.search_count_field}},
                                                    "filtered_count": {
                                                        "filter": self.aql_filter_statement,
                                                        "aggs": {
                                                            "_count": {
                                                                self.count_method: {"field": self.search_count_field}
                                                            }
                                                        },
                                                    },
                                                },
                                            },
                                        },
                                    }
                                },
                            }
                        },
                    }
                }
            }
        else:
            query = {
                "aggs": {
                    "f_group": {
                        "nested": {"path": f_meta_field.complex_full_name},
                        "aggs": {
                            "f_group": {
                                "terms": {"field": self.f_field, "size": self.f_limit, "order": {"_count": self.order}},
                                "aggs": {
                                    "reverse_nested_aggs": {
                                        "reverse_nested": {},
                                        "aggs": {
                                            "_count": {self.count_method: {"field": self.search_count_field}},
                                            "filtered_count": {
                                                "filter": self.aql_filter_statement,
                                                "aggs": {
                                                    "_count": {self.count_method: {"field": self.search_count_field}}
                                                },
                                            },
                                        },
                                    }
                                },
                            }
                        },
                    }
                }
            }
        return query

    def build_f_s_c_query(self, f_meta_field, s_meta_field, count_meta_field):
        query = {}
        if s_meta_field.complex_full_name and count_meta_field.complex_full_name:
            query = {
                "aggs": {
                    "f_group": {
                        "nested": {"path": f_meta_field.complex_full_name},
                        "aggs": {
                            "f_group": {
                                "terms": {
                                    "field": self.search_f_field,
                                    "size": self.f_limit,
                                },
                                "aggs": {
                                    "reverse_nested_aggs": {
                                        "reverse_nested": {},
                                        "aggs": {
                                            "s_group": {
                                                "nested": {"path": s_meta_field.complex_full_name},
                                                "aggs": {
                                                    "s_group": {
                                                        "terms": {
                                                            "field": self.search_s_field,
                                                            "size": self.s_limit,
                                                            "order": {"_count": self.order},
                                                        },
                                                        "aggs": {
                                                            "reverse_nested_aggs": {
                                                                "reverse_nested": {},
                                                                "aggs": {
                                                                    "count": {
                                                                        "nested": {
                                                                            "path": count_meta_field.complex_full_name
                                                                        },
                                                                        "aggs": {
                                                                            "_count": {
                                                                                self.count_method: {
                                                                                    "field": self.search_count_field
                                                                                },
                                                                            },
                                                                            "filtered_count": {
                                                                                "filter": self.aql_filter_statement,
                                                                                "aggs": {
                                                                                    "_count": {
                                                                                        self.count_method: {
                                                                                            "field": self.search_count_field
                                                                                        },
                                                                                    }
                                                                                },
                                                                            },
                                                                        },
                                                                    }
                                                                },
                                                            }
                                                        },
                                                    }
                                                },
                                            }
                                        },
                                    }
                                },
                            }
                        },
                    }
                }
            }
        if s_meta_field.complex_full_name and not count_meta_field.complex_full_name:
            query = {
                "aggs": {
                    "f_group": {
                        "nested": {"path": f_meta_field.complex_full_name},
                        "aggs": {
                            "f_group": {
                                "terms": {"field": self.search_f_field, "size": self.f_limit},
                                "aggs": {
                                    "reverse_nested_aggs": {
                                        "reverse_nested": {},
                                        "aggs": {
                                            "s_group": {
                                                "nested": {"path": s_meta_field.complex_full_name},
                                                "aggs": {
                                                    "s_group": {
                                                        "terms": {
                                                            "field": self.search_s_field,
                                                            "size": self.s_limit,
                                                            "order": {"_count": self.order},
                                                        },
                                                        "aggs": {
                                                            "reverse_nested_aggs": {
                                                                "reverse_nested": {},
                                                                "aggs": {
                                                                    "_count": {
                                                                        self.count_method: {
                                                                            "field": self.search_count_field
                                                                        },
                                                                    },
                                                                    "filtered_count": {
                                                                        "filter": self.aql_filter_statement,
                                                                        "aggs": {
                                                                            "_count": {
                                                                                self.count_method: {
                                                                                    "field": self.search_count_field
                                                                                },
                                                                            }
                                                                        },
                                                                    },
                                                                },
                                                            }
                                                        },
                                                    }
                                                },
                                            }
                                        },
                                    }
                                },
                            }
                        },
                    }
                }
            }

        if not s_meta_field.complex_full_name and count_meta_field.complex_full_name:
            query = {
                "aggs": {
                    "f_group": {
                        "nested": {"path": f_meta_field.complex_full_name},
                        "aggs": {
                            "f_group": {
                                "terms": {"field": self.search_f_field, "size": self.f_limit},
                                "aggs": {
                                    "reverse_nested_aggs": {
                                        "reverse_nested": {},
                                        "aggs": {
                                            "s_group": {
                                                "terms": {
                                                    "field": self.search_s_field,
                                                    "size": self.s_limit,
                                                    "order": {"_count": self.order},
                                                },
                                                "aggs": {
                                                    "reverse_nested_aggs": {
                                                        "reverse_nested": {},
                                                        "aggs": {
                                                            "count": {
                                                                "nested": {"path": count_meta_field.complex_full_name},
                                                                "aggs": {
                                                                    "_count": {
                                                                        self.count_method: {
                                                                            "field": self.search_count_field
                                                                        }
                                                                    },
                                                                    "filtered_count": {
                                                                        "filter": self.aql_filter_statement,
                                                                        "aggs": {
                                                                            "_count": {
                                                                                self.count_method: {
                                                                                    "field": self.search_count_field
                                                                                }
                                                                            }
                                                                        },
                                                                    },
                                                                },
                                                            }
                                                        },
                                                    }
                                                },
                                            }
                                        },
                                    }
                                },
                            }
                        },
                    }
                }
            }
        if not s_meta_field.complex_full_name and not count_meta_field.complex_full_name:
            query = {
                "aggs": {
                    "f_group": {
                        "nested": {"path": f_meta_field.complex_full_name},
                        "aggs": {
                            "f_group": {
                                "terms": {"field": self.search_f_field, "size": self.f_limit},
                                "aggs": {
                                    "reverse_nested_aggs": {
                                        "reverse_nested": {},
                                        "aggs": {
                                            "s_group": {
                                                "terms": {
                                                    "field": self.search_s_field,
                                                    "size": self.s_limit,
                                                    "order": {"_count": self.order},
                                                },
                                                "aggs": {
                                                    "reverse_nested_aggs": {
                                                        "reverse_nested": {},
                                                        "aggs": {
                                                            "_count": {
                                                                self.count_method: {"field": self.search_count_field}
                                                            },
                                                            "filtered_count": {
                                                                "filter": self.aql_filter_statement,
                                                                "aggs": {
                                                                    "_count": {
                                                                        self.count_method: {
                                                                            "field": self.search_count_field
                                                                        }
                                                                    }
                                                                },
                                                            },
                                                        },
                                                    }
                                                },
                                            }
                                        },
                                    }
                                },
                            }
                        },
                    }
                }
            }
        return query

    def build_simple_f_s_c_query(self, f_meta_field, s_meta_field, count_meta_field):
        query = {}
        if s_meta_field.complex_full_name and count_meta_field.complex_full_name:
            query = {
                "aggs": {
                    "f_group": {
                        "terms": {"field": self.search_f_field, "size": self.f_limit},
                        "aggs": {
                            "s_group": {
                                "nested": {"path": s_meta_field.complex_full_name},
                                "aggs": {
                                    "s_group": {
                                        "terms": {
                                            "field": self.search_s_field,
                                            "size": self.s_limit,
                                            "order": {"_count": self.order},
                                        },
                                        "aggs": {
                                            "reverse_nested_aggs": {
                                                "reverse_nested": {},
                                                "aggs": {
                                                    "count": {
                                                        "nested": {"path": count_meta_field.complex_full_name},
                                                        "aggs": {
                                                            "_count": {
                                                                self.count_method: {"field": self.search_count_field}
                                                            },
                                                            "filtered_count": {
                                                                "filter": self.aql_filter_statement,
                                                                "aggs": {
                                                                    "_count": {
                                                                        self.count_method: {
                                                                            "field": self.search_count_field
                                                                        }
                                                                    }
                                                                },
                                                            },
                                                        },
                                                    }
                                                },
                                            }
                                        },
                                    }
                                },
                            }
                        },
                    }
                }
            }
        if s_meta_field.complex_full_name and not count_meta_field.complex_full_name:
            query = {
                "aggs": {
                    "f_group": {
                        "terms": {"field": self.search_f_field, "size": self.f_limit},
                        "aggs": {
                            "s_group": {
                                "nested": {"path": s_meta_field.complex_full_name},
                                "aggs": {
                                    "s_group": {
                                        "terms": {
                                            "field": self.search_s_field,
                                            "size": self.s_limit,
                                            "order": {"_count": self.order},
                                        },
                                        "aggs": {
                                            "reverse_nested_aggs": {
                                                "reverse_nested": {},
                                                "aggs": {
                                                    "_count": {self.count_method: {"field": self.search_count_field}},
                                                    "filtered_count": {
                                                        "filter": self.aql_filter_statement,
                                                        "aggs": {
                                                            "_count": {
                                                                self.count_method: {"field": self.search_count_field}
                                                            }
                                                        },
                                                    },
                                                },
                                            }
                                        },
                                    }
                                },
                            }
                        },
                    }
                }
            }

        if not s_meta_field.complex_full_name and count_meta_field.complex_full_name:
            query = {
                "aggs": {
                    "f_group": {
                        "terms": {"field": self.search_f_field, "size": self.f_limit},
                        "aggs": {
                            "s_group": {
                                "terms": {
                                    "field": self.search_s_field,
                                    "size": self.s_limit,
                                    "order": {"count": self.order},
                                },
                                "aggs": {
                                    # "reverse_nested_aggs": {
                                    #     "reverse_nested": {},
                                    #     "aggs": {
                                    "count": {
                                        "nested": {"path": count_meta_field.complex_full_name},
                                        "aggs": {
                                            "count": {self.count_method: {"field": self.search_count_field}},
                                            "filtered_count": {
                                                "filter": self.aql_filter_statement,
                                                "aggs": {
                                                    "count": {self.count_method: {"field": self.search_count_field}}
                                                },
                                            },
                                        },
                                    }
                                },
                            }
                        },
                    }
                }
            }
        if not s_meta_field.complex_full_name and not count_meta_field.complex_full_name:
            query = {
                "aggs": {
                    "f_group": {
                        "terms": {"field": self.search_f_field, "size": self.f_limit},
                        "aggs": {
                            "s_group": {
                                "terms": {
                                    "field": self.search_s_field,
                                    "size": self.s_limit,
                                    "order": {"count": self.order},
                                },
                                # "aggs": {
                                #     "reverse_nested_aggs": {
                                #         "reverse_nested": {},
                                "aggs": {
                                    "count": {self.count_method: {"field": self.search_count_field}},
                                    "filtered_count": {
                                        "filter": self.aql_filter_statement,
                                        "aggs": {"count": {self.count_method: {"field": self.search_count_field}}},
                                    },
                                },
                            }
                        },
                    }
                }
            }
        return query

    def build_simple_f_c_query(self, f_meta_field, count_meta_field):
        query = {}
        if count_meta_field.complex_full_name:
            query = {
                "aggs": {
                    "f_group": {
                        "terms": {"field": self.search_f_field, "size": self.f_limit},
                        "aggs": {
                            "nested_counts": {
                                "nested": {"path": count_meta_field.complex_full_name},
                                "aggs": {
                                    "total_count": {self.count_method: {"field": self.search_count_field}},
                                    "filtered_count": {
                                        "filter": self.aql_filter_statement,
                                        "aggs": {
                                            "filtered_sum": {self.count_method: {"field": self.search_count_field}}
                                        },
                                    },
                                },
                            },
                            "sort_buckets": {
                                "bucket_sort": {
                                    "sort": [
                                        {"nested_counts>total_count": {"order": self.order}},
                                        {"nested_counts>filtered_count>filtered_sum": {"order": self.order}},
                                    ],
                                    "size": self.f_limit,
                                }
                            },
                        },
                    }
                }
            }
        else:
            query = {
                "aggs": {
                    "f_group": {
                        "terms": {"field": self.search_f_field, "size": self.f_limit},
                        "aggs": {
                            "count": {self.count_method: {"field": self.search_count_field}},
                            "filtered_count": {
                                "filter": self.aql_filter_statement,
                                "aggs": {"count": {self.count_method: {"field": self.search_count_field}}},
                            },
                            "sort_buckets": {
                                "bucket_sort": {
                                    "sort": [
                                        {"count": {"order": self.order}},
                                        {"filtered_count>count": {"order": self.order}},
                                    ],
                                    "size": self.f_limit,
                                }
                            },
                        },
                    }
                }
            }
        return query

    def build_c_query(self, count_meta_field):
        if count_meta_field.complex_full_name:
            query = {
                "aggs": {
                    "count": {
                        "nested": {"path": count_meta_field.complex_full_name},
                        "aggs": {
                            "count": {self.count_method: {"field": self.search_count_field}},
                            "filtered_count": {
                                "filter": self.aql_filter_statement,
                                "aggs": {"count": {self.count_method: {"field": self.search_count_field}}},
                            },
                        },
                    }
                }
            }
        else:
            query = {
                "aggs": {
                    "count": {self.count_method: {"field": self.search_count_field}},
                    "filtered_count": {
                        "filter": self.aql_filter_statement,
                        "aggs": {"count": {self.count_method: {"field": self.search_count_field}}},
                    },
                }
            }
        return query
